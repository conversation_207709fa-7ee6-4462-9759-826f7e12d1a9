import RegisterTechnicianPage from '@/modules/register-provider/RegisterTechnician'
import { IRegisterTechnicianForm } from '@/modules/register-provider/types'
import { apiService } from '@/services'
import { Metadata } from 'next'

export const dynamic = 'force-dynamic'
export const metadata: Metadata = {
  title: 'Register As Technician',
  description:
    'Register as a technician to provide maintenance and repair services to customers and manage your work easily through the platform.',
}

const Page = async () => {
  const res = await apiService({
    path: '/user/profile',
  })

  const userData: IRegisterTechnicianForm = res?.data?.data

  return <RegisterTechnicianPage userData={userData} />
}

export default Page
