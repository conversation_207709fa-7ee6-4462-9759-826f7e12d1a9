import { SidebarMenuButton, SidebarMenuItem } from '@/components/ui/sidebar'
import { ISidebarLink } from './sidebarLinks'
import { Button } from '@/components/ui/button'
import { SidebarMenuComponent } from './SidebarMenuComponent'
import { cn } from '@/lib/utils'
import { useTranslations } from 'next-intl'
import { usePathname } from 'next/navigation'
import Link from 'next/link'

export const SidebarLinkWithMenu = ({ item }: { item: ISidebarLink }) => {
  const t = useTranslations()
  const pathname = usePathname()
  // const isActive = item.groupName && !!matchPath({ path: item.groupName || '', end: false }, pathname)
  const isActive = pathname

  return (
    <SidebarMenuItem key={item.title}>
      <SidebarMenuButton asChild className={cn('cursor-pointer', isActive && 'bg-primary/20')}>
        {item.url ? (
          <Link href={item.url}>
            {item.icon && <item.icon />}
            {item.title && <span>{t(`navbar.${item.title}`)}</span>}
          </Link>
        ) : (
          <Button>
            {item.icon && <item.icon />}
            {item.title && <span>{t(`navbar.${item.title}`)}</span>}
          </Button>
        )}
      </SidebarMenuButton>
      {item.menu && <SidebarMenuComponent menuItems={item.menu} />}
    </SidebarMenuItem>
  )
}
