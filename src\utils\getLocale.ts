import { match as matchLocale } from '@formatjs/intl-localematcher'
import Negotiator from 'negotiator'

import { env } from '@/config/environment'
import { i18n } from '@/i18n-config'

import type { NextRequest } from 'next/server'

export const getLocale = (request: NextRequest): string | undefined => {
  const negotiatorHeader: Record<string, string> = {}

  const { headers } = request
  const cookie = headers.get('cookie')?.split(';')

  request.headers.forEach((value, key) => (negotiatorHeader[key] = value))

  //@ts-expect-error
  const locales: string[] = i18n.locales

  const languages = new Negotiator({ headers: negotiatorHeader }).languages(locales)

  const locale = matchLocale(locales, languages, i18n.defaultLocale)

  const language = cookie?.find((c) => c.trim().startsWith(env.LANGUAGE))?.split('=')[1]

  return language ?? locale
}
