// utils
import { cn } from '@/lib/utils'

// hooks
import useDatePicker from '@/components/filters/DatePickerFilter/useDatePicker'

// components
import { DatePicker } from '@/components/ui/datePicker'

// types
import type { IDatePickerProps } from '@/components/filters/DatePickerFilter/types'

export default function DatePickerFilter({
  className,
  label,
  name,
  queryName = { start: 'date_from', end: 'date_to' },
  mode = 'range',
  placeholder,
}: IDatePickerProps) {
  const { t, handleSelect, removeQueryFilter, getDefaultValue } = useDatePicker({
    name,
    queryName,
    mode,
  })

  return (
    <DatePicker
      label={label}
      placeholder={placeholder || t('label.select_date')}
      mode={mode}
      onChange={(date) => handleSelect(date)}
      className={cn('w-full', className)}
      onClear={removeQueryFilter}
      defaultValue={getDefaultValue()}
    />
  )
}
