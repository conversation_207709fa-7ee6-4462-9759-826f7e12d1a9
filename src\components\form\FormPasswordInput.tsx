import { useFormContext } from 'react-hook-form'
import { Input } from '@/components/ui/input'
import { FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form'
import { useFormWrapperContext } from '@/components/core/FormWrapper'
import { Eye, EyeOff } from 'lucide-react'
import { useState } from 'react'
import { Button } from '../ui/button'

interface FormInputProps extends Omit<React.InputHTMLAttributes<HTMLInputElement>, 'onChange'> {
  name: string
  label?: string
  onChange?: (newValue: string) => void
}

export function FormPasswordInput({ name, label, onChange, ...props }: FormInputProps) {
  const { control } = useFormContext()
  const { errors } = useFormWrapperContext()
  const [showPassword, setShowPassword] = useState(false)

  const handleShowPassword = () => {
    setShowPassword(!showPassword)
  }

  return (
    <FormField
      control={control}
      name={name}
      render={({ field }) => (
        <FormItem>
          {label && <FormLabel>{label}</FormLabel>}
          <FormControl>
            <div className="relative">
              <Input
                {...field}
                {...props}
                type={showPassword ? 'text' : 'password'}
                aria-invalid={!!errors[name]}
                onChange={(newValue) => {
                  field.onChange(newValue)
                  onChange && onChange(newValue.target.value)
                }}
                suffix={
                  <Button
                    type="button"
                    variant="ghost"
                    aria-label={showPassword ? 'Hide password' : 'Show password'}
                    onClick={handleShowPassword}
                    className="!min-w-fit"
                  >
                    {showPassword ? <EyeOff /> : <Eye />}
                  </Button>
                }
              />
            </div>
          </FormControl>
          <FormMessage />
        </FormItem>
      )}
    />
  )
}
