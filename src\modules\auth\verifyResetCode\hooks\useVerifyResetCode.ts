import { getCookie, setCookie } from 'cookies-next'
import { useTranslations } from 'next-intl'
import { useEffect, useRef, useState } from 'react'
import { useRouter } from 'next/navigation'
import { IVerifyResetCode, PhoneData } from '@/types'
import { IFormWrapper } from '@/components/core/FormWrapper'
import { toast } from 'sonner'
import { Routes } from '@/routes/routes'
import { signIn } from 'next-auth/react'
import useApi from '@/hooks/useApi'
import { createFormData } from '@/utils/createFormData'
import { observer } from '@/utils/observer'

const defaultValues: IVerifyResetCode = {
  identifier: '',
  country_code: '966',
  otp: '',
  device_id: '',
  firebase_token: '',
}

const useVerifyResetCode = ({
  type,
  onClose,
  openDialog,
}: {
  type: 'login' | 'register'
  onClose: () => void
  openDialog: boolean
}) => {
  const t = useTranslations()
  const router = useRouter()

  const [isOpenSuccesDialog, setIsOpenSuccesDialog] = useState(false)
  const [isPending, setIsPending] = useState(false)
  const [phone, setPhone] = useState<PhoneData | undefined>(undefined)

  useEffect(() => {
    const phoneCookie = getCookie('phone')

    // Check if cookie exists and is not undefined/null/invalid
    if (phoneCookie && phoneCookie !== 'undefined' && phoneCookie !== 'null' && typeof phoneCookie === 'string') {
      try {
        const parsedPhone = JSON.parse(phoneCookie) as PhoneData
        setPhone(parsedPhone)
      } catch (error) {
        console.error('Error parsing phone cookie:', error)
        setPhone(undefined)
      }
    } else {
      setPhone(undefined)
    }
  }, [])

  const handleCloseSuccessDialog = () => {
    setIsOpenSuccesDialog(false)
    router.push(Routes.HOME)
  }

  const formRef = useRef<IFormWrapper>(null)
  const [isAllowed, setIsAllowed] = useState(false)

  const fcmToken = getCookie('fcm_token') || ''
  const deviceId = getCookie('device_id') || ''

  const { action: allowedToSendResetCode } = useApi({
    path: '/user/auth/can-send-otp',
    method: 'POST',
    handleSuccess: false,
    onSuccess: (state) => {
      setIsAllowed(state.data.data)
    },
  })

  const onSubmit = async (payload: IVerifyResetCode) => {
    try {
      setIsPending(true)

      const result = await signIn('credentials', {
        redirect: false,
        identifier: phone?.identifier,
        country_code: phone?.country_code,
        otp: payload.otp,
        device_id: deviceId,
        firebase_token: fcmToken,
      })

      if (result?.error) {
        toast.error(result.error)
      } else if (result?.ok) {
        toast.success(t('auth.code_verified_successfully'))
        onClose()

        if (type === 'login') {
          router.push(Routes.HOME)
        } else {
          setIsOpenSuccesDialog(true)
        }
      }
    } catch (error) {
      console.error('Verification error:', error)
    } finally {
      setIsPending(false)
    }
  }

  const { action, isPending: isisPendingResendCode } = useApi({
    path: '/user/auth/otp/send',
    method: 'POST',
    handleSuccess: false,
    onSuccess: (data) => {
      // Store phone data for OTP verification
      if (data?.phone) {
        setCookie('phone', JSON.stringify(data.phone))
      }
      toast.success(t('auth.resend_code_success'))
      // Reset the counter when resend is successful
      observer.fire('handelReCounter')
    },
  })

  const handleResendCode = () => {
    if (!phone?.identifier) {
      toast.error(t('auth.phone_required'))
      return
    }
    const formdata = createFormData(phone)
    action(formdata)
  }

  useEffect(() => {
    const formdata = new FormData()
    formdata.append('identifier', phone?.identifier ?? '')
    formdata.append('country_code', phone?.country_code ?? '')

    if (openDialog) {
      allowedToSendResetCode(formdata)
    }
  }, [openDialog])

  useEffect(() => {
    const handlePopState = () => {
      sessionStorage.removeItem('counter')
    }

    window.addEventListener('popstate', handlePopState)
  }, [])

  return {
    t,
    phone,
    isPending,
    isisPendingResendCode,
    onSubmit,
    defaultValues,
    handleResendCode,
    formRef,
    isAllowed,
    setIsAllowed,
    isOpenSuccesDialog,
    setIsOpenSuccesDialog,
    handleCloseSuccessDialog,
  }
}

export default useVerifyResetCode
