import { <PERSON><PERSON><PERSON>, <PERSON><PERSON>, use<PERSON>s<PERSON><PERSON><PERSON>oader } from '@react-google-maps/api'
import { env } from '@/config/environment'
import { useTranslations } from 'next-intl'
import { useState, useRef } from 'react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Loader2, SearchIcon } from 'lucide-react'

const MapComponent = ({
  defaultLocation,
  selectedLocation,
  handleMapClick,
}: {
  defaultLocation: { lat: number; lng: number }
  selectedLocation: { lat: number; lng: number } | null
  handleMapClick: (event: google.maps.MapMouseEvent) => void
}) => {
  const t = useTranslations()
  const [searchValue, setSearchValue] = useState('')
  const mapRef = useRef<google.maps.Map | null>(null)

  const { isLoaded } = useJsApiLoader({
    id: 'google-map-script',
    googleMapsApiKey: env.NEXT_PUBLIC_GOOGLE_MAPS_API_KEY!,
    libraries: ['places'],
  })

  const handleLoad = (map: google.maps.Map) => {
    mapRef.current = map
  }

  const handleSearch = () => {
    if (!searchValue.trim() || !mapRef.current) return

    const service = new google.maps.Geocoder()
    service.geocode({ address: searchValue }, (results, status) => {
      if (status === 'OK' && results?.[0]) {
        const location = results[0].geometry.location
        mapRef.current?.panTo(location)
        mapRef.current?.setZoom(14)

        const fakeEvent = {
          latLng: location,
        } as unknown as google.maps.MapMouseEvent

        handleMapClick(fakeEvent)
      } else {
        alert(t('validations.address_not_found'))
      }
    })
  }

  if (!isLoaded) {
    return (
      <div className="flex items-center justify-center h-[500px]">
        <Loader2 className="w-4 h-4 animate-spin" />
      </div>
    )
  }

  return (
    <div className="relative w-full h-[500px]">
      <div className="absolute top-4 left-1/2 -translate-x-1/2 z-10 flex gap-3 justify-between items-center min-w-[300px] sm:min-w-[500px]">
        <Input
          containerClassName="w-full bg-bg-input-light dark:bg-bg-input-dark text-text-input-light dark:text-text-input-dark"
          value={searchValue}
          onChange={(e) => setSearchValue(e.target.value)}
          placeholder={t('label.search_here')}
          prefix={
            <div>
              <SearchIcon />
            </div>
          }
        />
        <Button className="max-w-[100px]" onClick={handleSearch}>
          {t('button.search')}
        </Button>
      </div>

      <GoogleMap
        mapContainerStyle={{ width: '100%', height: '100%' }}
        center={defaultLocation}
        zoom={10}
        onClick={handleMapClick}
        onLoad={handleLoad}
      >
        {selectedLocation && <Marker position={selectedLocation} />}
      </GoogleMap>
    </div>
  )
}

export default MapComponent
