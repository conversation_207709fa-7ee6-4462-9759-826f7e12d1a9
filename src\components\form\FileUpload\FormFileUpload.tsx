import { cn } from '@/lib/utils'
import { Button } from '@/components/ui/button'
import { FormField, FormItem } from '../../ui/form'
import { File, Plus, X } from 'lucide-react'
import Image from 'next/image'
import useFormFileUpload, { FormFileUploadProps } from './useFormFileUpload'

export interface IFormImageUploadProps extends FormFileUploadProps {
  children?: React.ReactNode
  className?: string
}

interface FilesPreviewProps {
  selectedFiles: (File | IFileResponse)[] | File | IFileResponse
  removeImage: (index: number, url?: string) => void
}

const FormFileUpload = ({
  name,
  multiple,
  maxSize = 10,
  maxLength,
  accept = 'image/*',
  className,
  children,
}: IFormImageUploadProps) => {
  const {
    selectedFiles,
    handleFileChange,
    removeImage,
    validateMaxLength,
    validateMaxSize,
    handleDragOver,
    handleDragLeave,
    handleDrop,
    getAllowedFileTypesDescription,
    isDragging,
    fileInputRef,
    errors,
    control,
    t,
  } = useFormFileUpload({
    name,
    multiple,
    maxSize,
    maxLength,
    accept,
  })
  return (
    <FormField
      name={name}
      control={control}
      render={() => (
        <div
          className={cn(
            'border border-input-border-light dark:border-input-border-dark rounded-lg transition-colors shadow-2xs bg-transparent overflow-hidden',
            isDragging && 'border-primary',
            errors[name] && 'bg-red-50 border-red-300 hover:border-red-400',
            className
          )}
        >
          <label htmlFor={`image-upload-${name}`} className="flex flex-col gap-2 w-full">
            <FormItem>
              {/* {label && <FormLabel>{label}</FormLabel>} */}
              <div onDrop={handleDrop} onDragOver={handleDragOver} onDragLeave={handleDragLeave}>
                {/* <CloudUpload size={24} />
                        <p className="font-medium text-sm">{t('label.file_upload_placeholder_title')}</p>
                        <p className="text-sm">
                          {t('label.file_upload_file_types', { accept: getAllowedFileTypesDescription() })}
                        </p>
                        <p className="text-sm text-center">
                          {`${t('label.file_upload_max_file_size', { maxSize })} ${maxLength && maxSize && t('label.and')} ${maxLength && t('label.file_upload_max_file_number', { maxLength })}`}
                        </p> */}
                {selectedFiles.length === 0 && children && children}

                {selectedFiles.length === 0 && !children && (
                  <span className="text-secondary flex items-center justify-center gap-2 font-medium min-h-[50px] w-full bg-bg-input-light dark:bg-bg-input-dark">
                    <Plus size={18} />
                    {t('label.add_image')}
                  </span>
                )}

                <input
                  multiple={multiple}
                  ref={fileInputRef}
                  id={`image-upload-${name}`}
                  type="file"
                  accept={accept}
                  onChange={handleFileChange}
                  className="hidden"
                />
              </div>
            </FormItem>
          </label>
          <FilesPreview selectedFiles={selectedFiles} removeImage={removeImage} />
        </div>
      )}
    />
  )
}

export default FormFileUpload

const FilesPreview = ({ selectedFiles, removeImage }: FilesPreviewProps) => {
  if (!selectedFiles) return <></>

  const filesAsArray = Array.isArray(selectedFiles) ? selectedFiles : [selectedFiles]

  return (
    <div
      className={cn(
        'grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-2 bg-bg-input-light dark:bg-bg-input-dark',
        filesAsArray.length !== 0 && 'p-4'
      )}
    >
      {filesAsArray.map((file, index) => {
        const isExistingFile = 'path' in file
        // const fileSize = isExistingFile ? 'N/A' : file.size
        const fileUrl = isExistingFile ? file.path : URL.createObjectURL(file)

        return (
          <div
            key={isExistingFile ? `${file.id}-${file.name}` : file.name}
            className="group rounded-md relative max-w-[120px] mx-auto"
          >
            {file.type.startsWith('image/') ? (
              <Image
                alt={file.name}
                src={fileUrl}
                className="object-cover object-center w-full h-full rounded-md"
                width={60}
                height={60}
              />
            ) : (
              <File size={24} />
            )}
            <Button
              type="button"
              variant="destructive"
              onClick={() => removeImage(index, isExistingFile ? undefined : fileUrl)}
              className="absolute top-[-4px] end-[-4px] opacity-0 group-hover:opacity-100 transition-opacity duration-300 pointer-events-none group-hover:pointer-events-auto !p-0 bg-bg-error-dark rounded-full w-6 h-6 flex items-center justify-center"
            >
              <X size={24} />
            </Button>
          </div>
        )
      })}
    </div>
  )
}
