import { env } from '@/config/environment'

interface AddressResult {
  address: string
  isSaudi: boolean
}

export const getAddressFromCoordinates = async (lat: number, lng: number): Promise<AddressResult> => {
  try {
    const response = await fetch(
      `https://maps.googleapis.com/maps/api/geocode/json?latlng=${lat},${lng}&key=${env.NEXT_PUBLIC_GOOGLE_MAPS_API_KEY}&language=ar`
    )

    const data = await response.json()

    if (data.results && data.results.length > 0) {
      const address = data.results[0].formatted_address

      const countryComponent = data.results[0].address_components.find((c: any) => c.types.includes('country'))

      const isSaudi = countryComponent?.short_name === 'SA'

      return { address, isSaudi }
    }
  } catch (error) {
    console.error('Error getting address:', error)
  }

  return { address: '', isSaudi: false }
}
