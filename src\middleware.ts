import createIntlMiddleware from 'next-intl/middleware'
import { NextResponse } from 'next/server'

import { getLocale } from '@/utils/getLocale'
import { i18n } from './i18n-config'

import type { NextRequest } from 'next/server'

const handleI18nRouting = createIntlMiddleware({
  locales: i18n.locales,
  defaultLocale: i18n.defaultLocale,
})

export function middleware(request: NextRequest) {
  const { pathname } = request.nextUrl

  if (pathname === '/firebase-messaging-sw.js') {
    return NextResponse.next()
  }

  // Handle root path specifically
  if (pathname === '/') {
    const locale = getLocale(request)
    return NextResponse.redirect(new URL(`/${locale}`, request.url))
  }

  const response = handleI18nRouting(request)

  const pathnameHasLocale = i18n.locales.some(
    (locale) => pathname.startsWith(`/${locale}/`) || pathname === `/${locale}`
  )

  const locale = getLocale(request)

  if (!pathnameHasLocale) {
    return NextResponse.redirect(new URL(`/${locale}${pathname}`, request.url))
  }

  return response
}

export const config = {
  matcher: ['/((?!api|_next/static|_next/image|favicon.ico|firebase-messaging-sw.js).*)'],
}
