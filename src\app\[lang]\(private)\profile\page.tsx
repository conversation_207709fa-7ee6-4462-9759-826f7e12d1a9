import Profile from '@/modules/profile'
import { apiService } from '@/services'
import { Metadata } from 'next'
import { getTranslations } from 'next-intl/server'

export const metadata: Metadata = {
  title: 'Profile',
  description: 'View and manage your personal profile information, settings, and preferences.',
}

const ProfilePage = async () => {
  const t = await getTranslations()
  const data = await apiService({
    path: '/user/profile',
    next: {
      tags: ['profile-data'],
    },
  })
  return <Profile data={data?.data?.data} title={t('navbar.profile')} />
}

export default ProfilePage
