import { array, mixed, object, string } from 'yup'

export const schema = (t: any) => {
  return object({
    name: string().required(),
    phone: string().required(),
    username: string()
      .required()
      .min(3)
      .max(20)
      .matches(/^[a-zA-Z0-9_-]+$/),
    email: string().email().max(255),

    location: string().required(),
    bank_name: string().max(100).required(),
    bank_account_number: string()
      .matches(/^[0-9]+$/)
      .min(10)
      .max(14)
      .required(),
    iban: string()
      .matches(/^[A-Za-z0-9]+$/)
      .length(24)
      .test('starts-with-country-code', t('validations.invalid_iban_format'), (value) => {
        if (!value) return false
        return /^[A-Z]{2}/.test(value)
      })
      .required(),
  })
}
