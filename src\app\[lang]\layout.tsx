// ** Next **
import { Cairo } from 'next/font/google'

import type { Metadata } from 'next'
// ** Styles **
import '@/styles/globals.css'

// ** I18N **
import { getMessages } from 'next-intl/server'

import { getServerAuthSession } from '@/config/auth'
import { i18n } from '@/i18n-config'

// ** Providers **
import Providers from './providers'

import type { Locale } from '@/i18n-config'

// ** Font **
const cairo = Cairo({
  subsets: ['latin'], // 👈 add "arabic" too if needed
  weight: ['400', '500', '600', '700'], // choose the weights you need
  variable: '--font-cairo', // for Tailwind CSS (optional)
})

// ** Metadata **
export const metadata: Metadata = {
  title: {
    template: '%s | <App_Name>',
    default: '<App_Name>',
  },
  description: 'Write a description for your website',
}

// ** Generate Static Params **
export async function generateStaticParams() {
  return i18n.locales.map((locale) => ({ lang: locale }))
}

interface IProps {
  children: React.ReactNode
  params: Promise<{ lang: Locale }>
}

export default async function RootLayout({ children, params }: Readonly<IProps>) {
  const resolvedParams = await params
  const messages = await getMessages()
  const session = await getServerAuthSession()

  return (
    <html lang={resolvedParams.lang} dir={resolvedParams.lang === 'ar' ? 'rtl' : 'ltr'} suppressHydrationWarning>
      <body className={cairo.className} suppressHydrationWarning>
        <Providers {...{ session, locale: resolvedParams.lang, messages }}>{children}</Providers>
      </body>
    </html>
  )
}
