'use client'
import { actionService, ActionServiceProps } from '@/services/actionService'
import { startTransition, useActionState, useEffect } from 'react'
import useError from '@/hooks/useError'

interface Props extends ActionServiceProps {
  handleError?: boolean
  handleSuccess?: boolean
  onSuccess?: (res: any) => void
  onError?: (error: any) => void
  intermediate?: boolean
}

interface ApiReturn<T> {
  state: ActionServiceReturn<T>
  action: (payload?: any) => void
  isPending: boolean
}

const useApi = <T>({
  handleError = true,
  handleSuccess = true,
  onSuccess,
  onError,
  intermediate,
  ...props
}: Props): ApiReturn<T> => {
  const { handleErrors } = useError()
  const serverAction = actionService.bind(null, props)
  const [state, serviceAction, isPending] = useActionState(serverAction, { status: false })

  const action = (payload?: any) => {
    startTransition(() => {
      serviceAction(payload)
    })
  }

  useEffect(() => {
    if (intermediate) {
      action()
    }
  }, [])

  useEffect(() => {
    if (state.status) {
      onSuccess && onSuccess(state.data)

      if (handleSuccess) {
        // if handleSuccess true handle success
        return
      }
    } else if (state.error) {
      onError && onError(state)

      if (handleError) {
        // if handleError true use useError
        handleErrors(state.error)
      }
    }
  }, [state])

  return { state: state as ActionServiceReturn<T>, action, isPending }
}

export default useApi
