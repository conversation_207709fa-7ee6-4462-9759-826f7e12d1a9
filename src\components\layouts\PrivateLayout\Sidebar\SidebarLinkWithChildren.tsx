import {
  SidebarMenuButton,
  SidebarMenuItem,
  SidebarMenuSub,
  SidebarMenuSubButton,
  SidebarMenuSubItem,
} from '@/components/ui/sidebar'
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@radix-ui/react-collapsible'
import { ChevronLeft, ChevronRight } from 'lucide-react'
import { ISidebarLink } from './sidebarLinks'
import { SidebarLinkWithMenu } from './SidebarLinkWithMenu'
import { cn } from '@/lib/utils'
import { useTranslations, useLocale } from 'next-intl'
import Link from 'next/link'
import { usePathname } from 'next/navigation'

export const SidebarLinkWithChildren = ({ item }: { item: ISidebarLink }) => {
  const isRtl = useLocale() === 'ar'
  const t = useTranslations()
  const pathname = usePathname()
  // const isActive = item.groupName && !!matchPath({ path: item.groupName || '', end: false }, pathname)
  const isActive = pathname

  return (
    <Collapsible key={item.title} asChild defaultOpen={true} className="group/collapsible">
      <SidebarMenuItem>
        <CollapsibleTrigger asChild className={cn('cursor-pointer', isActive && 'bg-primary/20')}>
          <SidebarMenuButton tooltip={item.title}>
            {item.icon && <item.icon />}
            {item.title && <span>{t(`navbar.${item.title}`)}</span>}
            {isRtl ? (
              <ChevronLeft className="ms-auto transition-transform duration-200 group-data-[state=open]/collapsible:-rotate-90" />
            ) : (
              <ChevronRight className="ms-auto transition-transform duration-200 group-data-[state=open]/collapsible:rotate-90" />
            )}
          </SidebarMenuButton>
        </CollapsibleTrigger>
        <CollapsibleContent>
          <SidebarMenuSub>
            {item.children?.map((child) => {
              if (child.children) return <SidebarLinkWithChildren key={child.title} item={child} />
              if (child.menu) return <SidebarLinkWithMenu key={child.title} item={child} />
              return <SidebarGroupItem key={child.title} item={child} />
            })}
          </SidebarMenuSub>
        </CollapsibleContent>
      </SidebarMenuItem>
    </Collapsible>
  )
}

const SidebarGroupItem = ({ item }: { item: ISidebarLink }) => {
  const t = useTranslations()
  const pathname = usePathname()
  // const isActive = !!matchPath({ path: item.url || '', end: true }, pathname)
  const isActive = pathname

  return (
    <SidebarMenuSubItem key={item.title}>
      <SidebarMenuSubButton asChild className={cn(isActive && 'bg-primary/20')}>
        <Link href={item.url || ''}>
          {item.icon && <item.icon />}
          <span>{t(`navbar.${item.title}`)}</span>
        </Link>
      </SidebarMenuSubButton>
    </SidebarMenuSubItem>
  )
}
