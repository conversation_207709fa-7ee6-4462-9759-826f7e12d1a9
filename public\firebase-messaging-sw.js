importScripts("https://www.gstatic.com/firebasejs/10.11.1/firebase-app-compat.js");
importScripts("https://www.gstatic.com/firebasejs/10.11.1/firebase-messaging-compat.js");

firebase.initializeApp({
  "apiKey": "AIzaSyDOUJKMYh-ORyZ7wKyRwBflOllyz5jBe18",
  "authDomain": "damaty-f3df9.firebaseapp.com",
  "projectId": "damaty-f3df9",
  "storageBucket": "damaty-f3df9.firebasestorage.app",
  "messagingSenderId": "393576960112",
  "appId": "1:393576960112:web:cffe6c62c7b72caf4364b5",
  "vapidKey": "BGw7LGidwXvE1PpT8FCjdTx4pUN2hU8iSL5sWUPgN9ybSW5xojyTQv2OqyzkoEBeReDhg8zh9k92HJ6_5xdg11I"
});

const messaging = firebase.messaging();

messaging.onBackgroundMessage(function (payload) {
  const title = payload?.notification?.title || "Notification";
  const options = {
    body: payload?.notification?.body || '',
  };
  self.registration.showNotification(title, options);
});
