import { useTranslations } from 'next-intl'

export interface IHeaderPage {
  title: string
  description?: string
}

const HeaderPage = ({ title, description }: IHeaderPage) => {
  const t = useTranslations()
  return (
    <div className="flex flex-col gap-1 items-center">
      <h1 className="lg:text-2xl md:text-xl text-lg font-bold text-text-head-light dark:text-text-head-dark">
        {t(`auth.${title}`)}
      </h1>
      <h2 className="text-center text-base sm:text-lg md:text-xl  text-text-desc-light max-w-[524px] dark:text-text-desc-dark">
        {t(`auth.${description}`)}
      </h2>
    </div>
  )
}

export default HeaderPage
