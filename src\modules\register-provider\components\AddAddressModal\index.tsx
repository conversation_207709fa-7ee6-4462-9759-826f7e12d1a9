import { FormWrapper } from '@/components/core/FormWrapper'
import useAddAddressModal from './useAddAddressModal'
import { schema } from './form.validation'
import { IAddAddressForm } from '../../types'
import FormHeader from '../FormHeader'
import { Button } from '@/components/ui/button'
import { FormInput, FormTextArea } from '@/components/form'
import { FormSwitch } from '@/components/form/FormSwitch'
import { FormRadioInput } from '@/components/form/FormRadioInput'
import { MapPin } from 'lucide-react'

const defaultValues: IAddAddressForm = {
  city: '',
}

const AddAddressModal = () => {
  const { formRef, handleSubmit, isPending, t } = useAddAddressModal()
  const AddAddressFormSchema = schema(t)
  return (
    <div className="bg-bg-form-light dark:bg-bg-form-dark p-6 rounded-[10px] w-full max-w-[750px] mx-auto">
      <FormWrapper ref={formRef} schema={AddAddressFormSchema} defaultValues={defaultValues} onSubmit={handleSubmit}>
        <FormHeader title={t('label.add_new_address')}>
          <FormSwitch name="set_as_default" label={t('label.set_as_default')} />
        </FormHeader>

        <div className="space-y-2">
          <FormRadioInput
            name="address_type"
            label={t('label.address_type')}
            options={[
              { label: t('label.company'), value: 'company' },
              { label: t('label.home'), value: 'home' },
              { label: t('label.office'), value: 'office' },
            ]}
          />
          <FormInput
            containerClassName="flex"
            name="name"
            label={t('label.street_name')}
            placeholder={t('label.enter_street_name')}
            required
            suffix={
              <Button
                // onClick={() => setIsOpenModal(true)}
                type="button"
                variant="text"
                className="p-0 !w-fit border-0"
              >
                <MapPin className="text-black-500 dark:text-white" />
              </Button>
            }
          />
          <FormInput
            containerClassName="flex"
            name="name"
            label={t('label.area_and_city')}
            placeholder={t('label.area_or_city')}
            required
          />
          <FormInput
            containerClassName="flex"
            name="name"
            label={t('label.district_name')}
            placeholder={t('label.enter_district_name')}
            required
          />
          <FormInput
            containerClassName="flex"
            name="name"
            label={t('label.building_name')}
            placeholder={t('label.enter_building_name')}
            required
          />
          <FormInput
            containerClassName="flex"
            name="name"
            label={t('label.floor_number')}
            placeholder={t('label.enter_floor_number')}
            required
          />
          <FormInput
            containerClassName="flex"
            name="name"
            label={t('label.apartment_number')}
            placeholder={t('label.enter_apartment_number')}
            required
          />
          <FormInput
            containerClassName="flex"
            name="name"
            label={t('label.location_from_map')}
            placeholder={t('label.location')}
            required
            suffix={
              <Button
                // onClick={() => setIsOpenModal(true)}
                type="button"
                variant="text"
                className="p-0 !w-fit border-0"
              >
                <MapPin className="text-black-500 dark:text-white" />
              </Button>
            }
          />
        </div>
        <FormTextArea
          name="name"
          label={t('label.additional_notes')}
          placeholder={t('label.enter_additional_notes')}
          required
        />

        <Button className="mt-9 mb-3" type="submit" isLoading={isPending}>
          {t('button.create_new_account')}
        </Button>
      </FormWrapper>
    </div>
  )
}

export default AddAddressModal
