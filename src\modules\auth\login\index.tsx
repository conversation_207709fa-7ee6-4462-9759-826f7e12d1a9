'use client'

import { FormWrapper } from '@/components/core/FormWrapper'
import { FormPhoneInput } from '@/components/form'
import { Button } from '@/components/ui/button'
import Link from 'next/link'
import { loginSchema } from '@/modules/auth/schema'
import useLogin from '@/modules/auth/login/useLogin'
import HeaderPage from '@/modules/auth/components/HeaderPage'
import { Routes } from '@/routes/routes'
import VerifyOtpDialog from '@/modules/auth/verifyResetCode'

const defaultValues = {
  phone: {
    identifier: '',
    country_code: '966',
  },
}

const Login = () => {
  const { t, handleSubmit, isPending, isDialogOpen, handleDialogClose } = useLogin()

  const schema = loginSchema()

  return (
    <>
      <HeaderPage title="login" description="login_desc" />
      <FormWrapper
        schema={schema}
        defaultValues={defaultValues}
        onSubmit={handleSubmit}
        className="sm:w-[380px] md:w-[550px] w-full"
      >
        <FormPhoneInput
          phoneName="phone"
          placeholder={t('label.phone')}
          label={t('label.phone')}
          disabledCountry
          required
        />
        <Button className="mt-9 mb-3 !w-full" type="submit" isLoading={isPending}>
          {t('auth.send_code')}
        </Button>
      </FormWrapper>
      <p className="text-primary-02 font-me mt-auto absolute bottom-6 text-center md:text-[18px] text-base">
        {t('auth.no_account_yet')}
        <Link className="text-secondary ms-0.5" href={Routes.AUTH.REGISTER}>
          {t('auth.register_now')}
        </Link>
      </p>

      <VerifyOtpDialog openDialog={isDialogOpen} onClose={handleDialogClose} type="login" />
    </>
  )
}

export default Login
