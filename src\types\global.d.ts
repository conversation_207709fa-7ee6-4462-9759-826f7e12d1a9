import type { Locale } from '@/i18n-config'

declare module '@/styles/globals.css'

declare global {
  interface IPageProps {
    params: Promise<{ lang: Locale; [key: string]: string }>
    searchParams: Promise<{ [key: string]: string | string[] | undefined }>
  }

  interface IDDl {
    id: number
    name: string
  }

  interface IStatus<T = string | number> {
    value: T
    label: string
  }

  interface ApiReturn<T> {
    data?: T
  }

  interface IFileResponse {
    id: number
    name: string
    path: string
    type: string
    extension: string
  }

  interface ActionServiceReturn<T> {
    data?: ApiReturn<T>
    status: boolean
    statusCode?: number
    message?: string
    error?: ErrorResponse
  }

  interface IPagination {
    total: number
    count: number
    per_page: number
    next_page_url: string
    prev_page_url: string
    current_page: number
    total_pages: number
  }

  type TData<T> = {
    items: T[]
    pagination: MetaOption
  }
  interface ILocation {
    lat: number
    lng: number
  }

  interface IProfile {
    id: number
    name: string
    email: string
    country_code: string
    phone: string
    image: string
    biometric_token: string
    biometric_enabled: boolean
    notification_enabled: boolean
    is_technician: boolean
    is_supplier: boolean
    is_rental_support: boolean
    bank_account_number: number
    bank_name: string
    iban: string
    location: ILocation
    username: string
    has_pending_rental_outlet_request: boolean
    has_pending_technician_request: boolean
    has_pending_supplier_request: boolean
    location_lat: number
    location_lng: number
  }
}

export {}
