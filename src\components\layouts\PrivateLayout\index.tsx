'use client'
import { SidebarInset, SidebarProvider } from '@/components/ui/sidebar'
import { PrivateHeader } from '@/components/layouts/PrivateLayout/Header'
import { AppSidebar } from '@/components/layouts/PrivateLayout/Sidebar/app-sidebar'
import { Suspense } from 'react'
import Loading from '@/components/core/Loading'
import ThemeSwitch from '@/components/shared/ThemeSwitch'
import { LocaleToggle } from '@/components/core/LocaleToggle'
import { Button } from '@/components/ui/button'
import { cn } from '@/lib/utils'
import { handleLogout } from '@/utils/handleLogout'
import { useTranslations } from 'next-intl'

const PrivateLayoutComponent = ({
  children,
}: Readonly<{
  children: React.ReactNode
}>) => {
  const t = useTranslations()

  return (
    // <SidebarProvider>
    //   <AppSidebar />
    //   <SidebarInset>
    //     <PrivateHeader />
    //     <Suspense
    //       fallback={
    //         <div className="h-full w-full flex items-center justify-center">
    //           <Loading />
    //         </div>
    //       }
    //     >
    <>
      <ThemeSwitch />
      <LocaleToggle />
      <Button
        variant="ghost"
        size="icon"
        className={cn(
          'h-8 w-8 justify-start flex items-center gap-3 px-3 py-2 rounded-lg text-lg font-medium transition-colors',
          'text-primary-02 hover:bg-gray-100 hover:text-primary-01'
        )}
        onClick={handleLogout}
      >
        {t('navbar.logout')}
      </Button>
      {/* <div className="flex flex-1 flex-col gap-4 p-4 pt-0">{children}</div> */}
      <div>{children}</div>
    </>
    //     </Suspense>
    //   </SidebarInset>
    // </SidebarProvider>
  )
}

export default PrivateLayoutComponent
