import ReactQuill from 'react-quill-new'

import { cn } from '@/utils/cn'

import type { TextEditorProps } from './TextEditor.types'

import 'react-quill-new/dist/quill.snow.css'

export function TextEditor({ className, placeholder, ...props }: TextEditorProps) {
  return (
    <ReactQuill
      className={cn(
        '[&_.ql-toolbar]:rounded-t-md! [&_.ql-container]:rounded-b-md! [&_.ql-picker-label_svg]:rtl:right-auto! [&_.ql-picker-label_svg]:rtl:left-0! dark:bg-input/30 !text-muted-foreground text-base md:text-sm ',
        className
      )}
      {...props}
    />
  )
}
