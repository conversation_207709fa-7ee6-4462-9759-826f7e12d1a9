import { useTranslations } from 'next-intl'

const FormHeader = ({ children, title }: { children?: React.ReactNode; title?: string }) => {
  const t = useTranslations()
  return (
    <>
      <div className="flex justify-between items-center">
        <h2 className="font-bold text-xl md:text-2xl text-text-head-light dark:text-text-head-dark">
          {title ? title : t('label.request_information')}
        </h2>
        {children}
      </div>
      <hr className="bg-divider-light dark:bg-divider-dark h-[1px] border-0 my-3 md:my-5" />
    </>
  )
}

export default FormHeader
