import BreadcrumbComponent from '@/components/shared/BreadcrumbComponent'
import PageHeading from '@/components/shared/PageHeading'
import { useTranslations } from 'next-intl'
import RegisterSupplierForm from './RegisterSupplierForm'
import { IRegisterSupplierForm } from '../types'

const RegisterSupplierPage = ({ userData }: { userData: IRegisterSupplierForm }) => {
  const t = useTranslations()
  return (
    <section className="container">
      <BreadcrumbComponent />
      <PageHeading text={t('register_provider.company_registration_note')} />
      <RegisterSupplierForm userData={userData} />
    </section>
  )
}

export default RegisterSupplierPage
