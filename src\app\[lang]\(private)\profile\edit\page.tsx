import Profile from '@/modules/profile'
import { apiService } from '@/services'
import { Metadata } from 'next'
import { getTranslations } from 'next-intl/server'

export const metadata: Metadata = {
  title: 'Profile Edit',
  description: 'Edit your personal profile information, settings, and preferences.',
}

const ProfileEditPage = async () => {
  const t = await getTranslations()
  const data = await apiService({
    path: '/user/profile',
    next: {
      tags: ['profile-data'],
    },
  })
  return <Profile data={data?.data?.data} title={t('navbar.edit_profile')} isEdit={true} />
}

export default ProfileEditPage
