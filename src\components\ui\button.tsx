import { Slot } from '@radix-ui/react-slot'
import { cva } from 'class-variance-authority'
import * as React from 'react'

import { cn } from '@/lib/utils'

import type { VariantProps } from 'class-variance-authority'
import { Loader2 } from 'lucide-react'

const buttonVariants = cva(
  "inline-flex items-center justify-center gap-2 whitespace-nowrap text-base transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none  aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive capitalize focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]` rounded-[10px] font-bold cursor-pointer w-full",
  {
    variants: {
      variant: {
        default: 'bg-primary text-white font-semibold dark:bg-secondary',
        outline: 'text-primary-02 border border-primary-02',
        secondary: 'bg-bg-action-light dark:bg-bg-action-dark text-primary',
        destructive:
          'bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60',
        ghost: 'hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50 ',
        link: 'text-primary underline-offset-4 hover:underline',
        text: 'text-primary-02 bg-bg-input-light dark:bg-bg-input-dark cursor-pointer rounded-none',
      },
      size: {
        default: 'h-[50px] px-4 py-2 has-[>svg]:px-3',
        sm: 'h-[40px] rounded-md text-base',
        lg: 'h-[56px]',
        icon: 'size-9',
      },
    },
    defaultVariants: {
      variant: 'default',
      size: 'default',
    },
  }
)

export type TButtonProps = React.ComponentProps<'button'> &
  VariantProps<typeof buttonVariants> & {
    asChild?: boolean
    permissionName?: string
    isLoading?: boolean
    disabled?: boolean
  }

function Button({
  className,
  variant,
  size,
  asChild = false,
  isLoading = false,
  disabled = false,
  // permissionName, //  prevent permissionName from being passed to the DOM.
  ...props
}: TButtonProps) {
  const Comp = asChild ? Slot : 'button'

  return (
    <Comp
      type="button"
      data-slot="button"
      className={cn(buttonVariants({ variant, size, className }))}
      {...props}
      disabled={isLoading || disabled}
    >
      {isLoading ? <Loader2 className="w-4 h-4 animate-spin" /> : props.children}
    </Comp>
  )
}

export { Button, buttonVariants }
