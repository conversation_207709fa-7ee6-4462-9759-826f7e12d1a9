import RegisterRentalOperatorPage from '@/modules/register-provider/RegisterRentalOperator'
import { apiService } from '@/services'
import { Metadata } from 'next'

export const dynamic = 'force-dynamic'
export const metadata: Metadata = {
  title: 'Register As Rental Operator',
  description:
    'Fill in the information below to register as an equipment rental operator and provide equipment to customers.',
}

const Page = async () => {
  const res = await apiService({
    path: '/user/profile',
  })
  const userData: IProfile = res?.data?.data

  return <RegisterRentalOperatorPage userData={userData} />
}

export default Page
