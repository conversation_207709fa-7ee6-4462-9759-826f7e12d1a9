'use client'

import { useTranslations } from 'next-intl'
import { useState } from 'react'
import useApi from '@/hooks/useApi'

import { toast } from 'sonner'

import { handleLogout } from '@/utils/handleLogout'

function useRemoveAccount() {
  const t = useTranslations()
  const [isOpenDialog, setIsOpenDialog] = useState(false)
  const [isOpenSuccessDialog, setIsOpenSuccessDialog] = useState(false)

  const { action, isPending } = useApi({
    path: `/user/profile/delete-account`,
    method: 'DELETE',
    handleSuccess: false,
    onSuccess: () => {
      toast.success(t('profile.remove_account_success'))
      setIsOpenSuccessDialog(true)
    },
  })

  const handleCloseSuccessDialog = () => {
    setIsOpenSuccessDialog(false)
    handleLogout()
  }

  const handleRemoveAccount = () => {
    action()
  }

  return {
    handleRemoveAccount,
    isPending,
    isOpenDialog,
    setIsOpenDialog,
    isOpenSuccessDialog,
    setIsOpenSuccessDialog,
    handleCloseSuccessDialog,
  }
}

export { useRemoveAccount }
