name: CI/CD Deployment

on:
  push:
    branches:
      - main # or your deployment branch

jobs:
  deploy:
    name: Deploy to Server
    runs-on: ubuntu-latest

    steps:
      - name: Deploy to server via SSH
        uses: appleboy/ssh-action@v1.0.0
        with:
          host: ${{ secrets.SERVER_HOST }}
          username: ${{ secrets.SERVER_USER }}
          key: ${{ secrets.SERVER_SSH_KEY }}
          port: 22
          script: |
            cd /home/<USER>/public_html
            git reset --hard main
            git pull origin main
            yarn install
            yarn build
            if pm2 describe benaa-website > /dev/null; then
              pm2 restart benaa-website
            else
              pm2 start ecosystem.config.js
            fi
