import { IFormWrapper } from '@/components/core/FormWrapper'
import useApi from '@/hooks/useApi'
import { Routes } from '@/routes/routes'
import { useTranslations } from 'next-intl'
import { useRouter } from 'next/navigation'
import { useEffect, useRef, useState } from 'react'
import { getAddressFromCoordinates } from '@/utils/getAddressFormCoordinates'
import generateBody from '@/utils/generateBody'

const useRegisterRentalOperatorForm = ({ userData }: { userData: IProfile }) => {
  const t = useTranslations()
  const formRef = useRef<IFormWrapper>(null)
  const router = useRouter()
  const [isOpenSuccessDialog, setIsOpenSuccessDialog] = useState(false)
  const { action, isPending } = useApi({
    path: '/auth/register',
    method: 'POST',
    handleSuccess: false,
    onSuccess: async () => {
      setIsOpenSuccessDialog(true)
    },
  })

  const handleCloseSuccessDialog = () => {
    setIsOpenSuccessDialog(false)
    router.push(Routes.HOME)
  }

  useEffect(() => {
    const fetchAddress = async () => {
      try {
        if (userData?.location?.lat && userData?.location?.lng) {
          const { address } = await getAddressFromCoordinates(userData.location.lat, userData.location.lng)

          formRef.current?.setValues({
            ...userData,
            location: address || null,
          })
        } else {
          formRef.current?.setValues({
            ...userData,
            location: '',
          })
        }
      } catch (error) {
        console.error('Error fetching address:', error)
      }
    }

    fetchAddress()
  }, [userData])

  const handleSubmit = (payload: IProfile) => {
    const formdata = generateBody(payload)
    formdata.append('location[lat]', payload.location_lat.toString())
    formdata.append('location[lng]', payload.location_lng.toString())
    action(formdata)
  }
  return {
    t,
    formRef,
    isPending,
    handleSubmit,
    isOpenSuccessDialog,
    handleCloseSuccessDialog,
  }
}

export default useRegisterRentalOperatorForm
