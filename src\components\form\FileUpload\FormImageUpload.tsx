import { cn } from '@/lib/utils'
import { Button } from '@/components/ui/button'
import { FormField, FormItem } from '../../ui/form'
import { File, X } from 'lucide-react'
import Image from 'next/image'
import useFormFileUpload, { FormFileUploadProps } from './useFormFileUpload'

import UploadImg from '/public/icons/document-upload.svg'
import UploadImgDark from '/public/icons/document-upload_dark.svg'
import { useTheme } from 'next-themes'

export interface IFormImageUploadProps extends FormFileUploadProps {
  children?: React.ReactNode
}

interface FilesPreviewProps {
  selectedFiles: (File | IFileResponse | string)[] | File | IFileResponse | string
  removeImage: (index: number, url?: string) => void
}

const FormImageUpload = ({
  name,
  multiple,
  maxSize = 10,
  maxLength,
  accept = 'image/*',
  children,
}: IFormImageUploadProps) => {
  const { theme } = useTheme()

  const {
    selectedFiles,
    handleFileChange,
    removeImage,
    validateMaxLength,
    validateMaxSize,
    handleDragOver,
    handleDragLeave,
    handleDrop,
    getAllowedFileTypesDescription,
    isDragging,
    fileInputRef,
    errors,
    control,
    t,
  } = useFormFileUpload({
    name,
    multiple,
    maxSize,
    maxLength,
    accept,
  })
  return (
    <FormField
      name={name}
      control={control}
      render={() => (
        <div
          className={cn(
            'flex flex-col gap-3 w-full',
            isDragging && 'border-primary',
            errors[name] && 'bg-red-50 border-red-300 hover:border-red-400'
          )}
        >
          <FilesPreview selectedFiles={selectedFiles} removeImage={removeImage} />
          <label htmlFor={`image-upload-${name}`} className="flex flex-col gap-2 w-full">
            <FormItem>
              {/* {label && <FormLabel>{label}</FormLabel>} */}
              <div onDrop={handleDrop} onDragOver={handleDragOver} onDragLeave={handleDragLeave}>
                {children || (
                  <div className="bg-bg-action-light dark:bg-bg-action-dark text-primary dark:text-white max-w-[250px] flex justify-center items-center gap-1 rounded-[10px] py-4 max-h-[50px] border border-input-border-light dark:border-input-border-dark">
                    <Image src={theme === 'dark' ? UploadImgDark : UploadImg} alt="upload" width={24} height={24} />
                    {t('label.upload_profile_image')}
                  </div>
                )}
                <input
                  multiple={multiple}
                  ref={fileInputRef}
                  id={`image-upload-${name}`}
                  type="file"
                  accept={accept}
                  onChange={handleFileChange}
                  className="hidden"
                />
              </div>
            </FormItem>
          </label>
        </div>
      )}
    />
  )
}

export default FormImageUpload

const FilesPreview = ({ selectedFiles, removeImage }: FilesPreviewProps) => {
  if (!selectedFiles) return <></>

  const filesAsArray = Array.isArray(selectedFiles) ? selectedFiles : [selectedFiles]

  return (
    <div
      className={cn(
        'grid grid-cols-1 gap-2 group',
        filesAsArray.length > 3 && 'xl:grid-cols-2',
        filesAsArray.length > 1 && 'md:grid-cols-2'
      )}
    >
      {filesAsArray.map((file, index) => {
        // Handle different file types: string URL, File object, or IFileResponse object
        const isStringUrl = typeof file === 'string'
        const isExistingFile = !isStringUrl && typeof file === 'object' && 'path' in file

        let fileUrl: string
        let fileName: string
        let fileType: string

        if (isStringUrl) {
          fileUrl = file
          fileName = 'Profile Image'
          fileType = 'image/jpeg' // Assume image for string URLs
        } else if (isExistingFile) {
          fileUrl = file.path
          fileName = file.name
          fileType = file.type || 'image/jpeg'
        } else {
          fileUrl = URL.createObjectURL(file)
          fileName = file.name
          fileType = file.type
        }

        return (
          <figure
            key={isStringUrl ? `url-${index}` : isExistingFile ? `${file.id}-${file.name}` : file.name}
            className="p-3 border border-input-border-light dark:border-input-border-dark rounded-[10px] relative max-w-[250px] max-h-[250px] flex justify-center items-center size-[250px]"
          >
            {fileType.startsWith('image/') ? (
              <Image
                alt={fileName}
                src={fileUrl}
                className="object-cover object-center rounded-[10px] max-h-[200px] max-w-[200px]"
                width={200}
                height={200}
              />
            ) : (
              <File size={24} />
            )}
            <Button
              type="button"
              variant="destructive"
              onClick={() => removeImage(index, isStringUrl || isExistingFile ? undefined : fileUrl)}
              className="absolute top-[-4px] end-[-4px] opacity-0 group-hover:opacity-100 transition-opacity duration-300 pointer-events-none group-hover:pointer-events-auto !p-0 bg-bg-error-dark rounded-full w-6 h-6 flex items-center justify-center"
            >
              <X size={24} />
            </Button>
          </figure>
        )
      })}
    </div>
  )
}
