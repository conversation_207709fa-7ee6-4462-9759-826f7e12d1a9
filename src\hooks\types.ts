// import { MetaOption } from '@/components/ui/Table/types';

export type THttpMethod = 'GET' | 'POST' | 'PUT' | 'PATCH' | 'DELETE'

//TODO: Handle error type based on backend response
export interface IError {
  status: boolean
  statusCode: number
  message: string
  errors: string[] | string
}

export interface IApiReturn<T> {
  // meta?: MetaOption;
  data: T | null
  error: IError | null
}

export interface IApiProps {
  url: string
  method: THttpMethod
  body?: Record<string, unknown>
  params?: Record<string, string>
  headers?: Record<string, string>
  noLoading?: boolean
}

export interface IApiHookReturn {
  loading: boolean
  api: <T>(apiProps: IApiProps) => Promise<IApiReturn<T>>
}
