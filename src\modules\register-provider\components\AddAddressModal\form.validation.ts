import { object, string, boolean, number, mixed } from 'yup'

export const schema = (t: any) => {
  return object({
    address_label: string().required(),

    street_name: string()
      .required()
      .min(2)
      .max(50)
      .matches(/^[a-zA-Z0-9\s.,'-]+$/, t('validations.street_name_length')),

    area_city: string().required(),

    region: string().required(),

    building_name: string()
      .max(50)
      .matches(/^[a-zA-Z0-9\s]*$/, t('validations.building_name_invalid'))
      .optional(),

    building_number: string()
      .max(10)
      .matches(/^[a-zA-Z0-9]*$/, t('validations.apartment_number_invalid'))
      .optional(),

    floor_number: string()
      .max(2)
      .matches(/^[0-9]*$/, t('validations.floor_number_numeric'))
      .optional(),

    geographic_location: mixed().required(),

    additional_note: string().max(150).optional(),

    set_as_default: boolean().optional(),
  })
}
