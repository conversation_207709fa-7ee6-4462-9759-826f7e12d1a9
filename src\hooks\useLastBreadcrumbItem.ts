import { TBreadcrumbItem } from '@/components/layouts/Breadcrumb'
import { observer } from '@/utils/observer'
import { useTranslations } from 'next-intl'
import { usePathname, useSearchParams } from 'next/navigation'
import { useEffect, useMemo, useState } from 'react'

const useLastBreadcrumbItem = () => {
  const t = useTranslations()
  const pathname = usePathname()
  const params = useSearchParams()

  const [paramsValuesAlternative, setParamsValuesAlternative] = useState<Record<string, string>>({})

  const reversedParams: Record<string, string> = useMemo(() => {
    return Object.fromEntries(Object.entries(params).map(([key, value]) => [value?.toString(), key.toString()]))
  }, [params])

  const currentRouteItems: TBreadcrumbItem[] = useMemo(() => {
    const path = pathname.split('/').filter(Boolean)
    return path.map((item, index) => ({
      label: reversedParams[item]
        ? `${
            paramsValuesAlternative[reversedParams[item]]
              ? paramsValuesAlternative[reversedParams[item]]
              : `:${reversedParams[item]}`
          }`
        : t(`navbar.${item}`),
      to: `/${path.slice(0, index + 1).join('/')}`,
    }))
  }, [t, pathname, reversedParams, paramsValuesAlternative])

  const [items, setItems] = useState<TBreadcrumbItem[]>([])

  useEffect(() => {
    setItems(currentRouteItems)
  }, [currentRouteItems])

  useEffect(() => {
    observer.subscribe('set_breadcrumb_items', setItems)
    observer.subscribe('set_breadcrumb_params', setParamsValuesAlternative)

    return () => {
      observer.unsubscribe('set_breadcrumb_items')
      observer.unsubscribe('set_breadcrumb_params')
    }
  }, [])

  const _items = items.length ? items : currentRouteItems

  return _items.length ? _items[_items.length - 1] : undefined
}

export default useLastBreadcrumbItem
