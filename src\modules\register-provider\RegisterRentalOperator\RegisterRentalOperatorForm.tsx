'use client'

import { FormWrapper } from '@/components/core/FormWrapper'
import { FormInput } from '@/components/form'
import { Button } from '@/components/ui/button'
import { schema } from './form.validation'
import useRegisterRentalOperatorForm from './useRegisterRentalOperatorForm'
import LocationInput from '../components/LocationInput'
import CustomDialog from '@/components/shared/CustomDialog'
import FormHeader from '../components/FormHeader'

const defaultValues: IProfile = {
  id: 0,
  name: '',
  email: '',
  country_code: '',
  phone: '',
  image: '',
}

const RegisterRentalOperatorForm = ({ userData }: { userData: IProfile }) => {
  const { t, isPending, handleSubmit, formRef, isOpenSuccessDialog, handleCloseSuccessDialog } =
    useRegisterRentalOperatorForm({ userData: userData })
  const rentalOperatorFormSchema = schema(t)
  return (
    <div className="bg-bg-form-light dark:bg-bg-form-dark p-6 rounded-[10px] w-full max-w-[750px] mx-auto">
      <FormHeader />
      <FormWrapper
        ref={formRef}
        schema={rentalOperatorFormSchema}
        defaultValues={defaultValues}
        onSubmit={handleSubmit}
      >
        <div className="space-y-2">
          <FormInput
            containerClassName="flex"
            name="name"
            label={t('label.full_name')}
            placeholder={t('label.full_name')}
            required
          />

          <FormInput
            name="phone"
            label={t('label.phone_number')}
            placeholder={t('label.enter_phone_number')}
            required
          />
          <FormInput name="email" label={t('label.email')} placeholder={t('label.enter_email')} required />

          <FormInput name="bank_name" label={t('label.bank_name')} placeholder={t('label.enter_bank_name')} required />

          <FormInput
            name="bank_account_number"
            label={t('label.account_number')}
            placeholder={t('label.enter_account_number')}
            required
          />
          <FormInput name="iban" label={t('label.iban_number')} placeholder={t('label.enter_iban')} required />

          <LocationInput {...userData} />
        </div>
        <Button className="mt-6" type="submit" isLoading={isPending}>
          {t('label.submit_request')}
        </Button>
      </FormWrapper>
      <CustomDialog
        openDialog={isOpenSuccessDialog}
        onClose={handleCloseSuccessDialog}
        title={t('dialog.request_submitted_successfully')}
        description={t('dialog.data_under_review')}
        renderButton={
          <Button type="button" className="w-full" onClick={handleCloseSuccessDialog}>
            {t('dialog.back')}
          </Button>
        }
      />
    </div>
  )
}

export default RegisterRentalOperatorForm
