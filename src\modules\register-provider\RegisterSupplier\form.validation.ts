import { object, string, array, mixed } from 'yup'

export const schema = (t: any) => {
  return object({
    supplier_type: string().required(),
    company_name: string().min(4).max(30).required(),
    username: string()
      .required()
      .min(3)
      .max(20)
      .matches(/^[a-zA-Z0-9_-]+$/),
    commercial_registration: string()
      .matches(/^[0-9]+$/, t('validations.commercial_registration_must_contain_only_numbers'))
      .length(10)
      .required(),
    tax_number: string()
      .matches(/^[0-9]+$/, t('validations.tax_number_must_contain_only_numbers'))
      .length(15)
      .required(),
    phone: string().required(),
    company_email: string().email().max(255).required(),
    email: string().email().max(255),
    image: mixed()
      .test('fileType', t('validations.invalid_image_format'), (value: any) => {
        if (!value) return true
        return ['image/jpeg', 'image/png'].includes(value?.type)
      })
      .test('fileSize', t('validations.image_size_exceeds_5mb'), (value: any) => {
        if (!value) return true
        return value.size <= 5 * 1024 * 1024
      }),

    location: string().required(),
    bank_name: string().max(100).required(),
    bank_account_number: string()
      .matches(/^[0-9]+$/)
      .min(10)
      .max(14)
      .required(),
    iban: string()
      .matches(/^[A-Za-z0-9]+$/)
      .length(24)
      .test('starts-with-country-code', t('validations.invalid_iban_format'), (value) => {
        if (!value) return false
        return /^[A-Z]{2}/.test(value)
      })
      .required(),
  })
}
