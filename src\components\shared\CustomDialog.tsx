import { Dialog, DialogContent, DialogTitle } from '../ui/dialog'

type CustomDialogProps = {
  openDialog: boolean
  onClose: () => void
  title?: string
  description?: string
  textContent?: React.ReactNode
  renderButton?: React.ReactNode
  icon?: React.ReactNode
}

export default function CustomDialog({
  openDialog,
  onClose,
  title,
  description,
  renderButton,
  icon,
  textContent,
}: CustomDialogProps) {
  return (
    <Dialog open={openDialog} onOpenChange={onClose}>
      <DialogContent className="pb-5">
        <DialogTitle className="flex justify-center items-center mb-5">{icon}</DialogTitle>

        <div className="flex flex-col gap-1 items-center mb-5">
          {textContent ? (
            textContent
          ) : (
            <>
              <p className=" md:text-lg text-base font-bold text-text-head-light dark:text-text-head-dark">{title}</p>
              <p className="text-center font-semibold text-sm sm:text-base  text-text-desc-light max-w-[524px] dark:text-text-desc-dark">
                {description}
              </p>
            </>
          )}
        </div>

        {renderButton}
      </DialogContent>
    </Dialog>
  )
}
