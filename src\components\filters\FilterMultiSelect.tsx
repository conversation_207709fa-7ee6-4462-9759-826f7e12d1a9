import { useQuery } from '@/store/queryContext/useQueryContext'
import { Label } from '../ui/label'
import { MultiSelect, MultiSelectProps } from '../ui/multi-select'
import { useTranslations } from 'next-intl'

export interface FilterMultiSelectProps<T> extends MultiSelectProps<T> {
  label?: string
  name: string
  placeholder?: string
}

const FilterMultiSelect = <T,>({ label, data, name, placeholder, ...props }: FilterMultiSelectProps<T>) => {
  const { forwardAddQuery, forwardQuery, forwardDeleteQuery } = useQuery()
  const t = useTranslations()

  const handleSelectFilter = (value: string[]) => {
    forwardAddQuery({ [name]: value.join(',') })
  }

  const handleClearFilter = () => {
    forwardDeleteQuery(name)
  }

  return (
    <>
      {label && <Label htmlFor={`${name}-multi-select`}>{label}</Label>}
      <MultiSelect
        id={`${name}-multi-select`}
        data={data}
        placeholder={placeholder || t('label.select_options')}
        defaultValue={forwardQuery?.[name]?.split(',') ?? []}
        onValueChange={handleSelectFilter}
        onClear={handleClearFilter}
        {...props}
      />
    </>
  )
}

export default FilterMultiSelect
