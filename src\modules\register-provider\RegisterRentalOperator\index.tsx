import BreadcrumbComponent from '@/components/shared/BreadcrumbComponent'
import PageHeading from '@/components/shared/PageHeading'
import { useTranslations } from 'next-intl'
import RegisterRentalOperatorForm from './RegisterRentalOperatorForm'

const RegisterRentalOperatorPage = ({ userData }: { userData: IProfile }) => {
  const t = useTranslations()
  return (
    <section className="container">
      <BreadcrumbComponent />
      <PageHeading text={t('register_provider.equipment_rental_note')} />
      <RegisterRentalOperatorForm userData={userData} />
    </section>
  )
}

export default RegisterRentalOperatorPage
