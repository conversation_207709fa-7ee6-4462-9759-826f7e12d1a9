import { IFormWrapper } from '@/components/core/FormWrapper'
import useApi from '@/hooks/useApi'
import { createFormData } from '@/utils/createFormData'
import { useTranslations } from 'next-intl'
import { useRef } from 'react'
import { IAddAddressForm } from '../../types'

const useAddAddressModal = () => {
  const t = useTranslations()
  const formRef = useRef<IFormWrapper>(null)
  const { action, isPending } = useApi({
    path: '/user/profile/upgrade-to-technician',
    method: 'POST',
    handleSuccess: false,
    onSuccess: async () => {
      // open success modal
    },
  })

  const handleSubmit = (payload: IAddAddressForm) => {
    const formdata = createFormData(payload)
    action(formdata)
  }
  return {
    t,
    formRef,
    isPending,
    handleSubmit,
  }
}

export default useAddAddressModal
