// app/hooks/useUserLocation.ts
'use client'

import { useEffect, useState, useRef } from 'react'

type Loc = { lat: number; lng: number } | null
type Status = 'idle' | 'requesting' | 'granted' | 'denied' | 'error'

export function useUserLocation({ watch = false } = {}) {
  const [location, setLocation] = useState<Loc>(null)
  const [status, setStatus] = useState<Status>('idle')
  const [error, setError] = useState<string | null>(null)
  const watcherId = useRef<number | null>(null)

  useEffect(() => {
    if (typeof window === 'undefined' || !('geolocation' in navigator)) {
      setError('Geolocation not available in this browser')
      setStatus('error')
      return
    }

    setStatus('requesting')

    const success = (pos: GeolocationPosition) => {
      const { latitude, longitude } = pos.coords
      setLocation({ lat: latitude, lng: longitude })
      setStatus('granted')
      setError(null)
    }

    const fail = (err: GeolocationPositionError) => {
      if (err.code === err.PERMISSION_DENIED) {
        setStatus('denied')
        setError('Permission denied')
      } else {
        setStatus('error')
        setError(err.message)
      }
    }

    if (watch) {
      watcherId.current = navigator.geolocation.watchPosition(success, fail, {
        enableHighAccuracy: true,
        maximumAge: 10000,
        timeout: 20000,
      })
    } else {
      navigator.geolocation.getCurrentPosition(success, fail, {
        enableHighAccuracy: true,
        maximumAge: 10000,
        timeout: 20000,
      })
    }

    return () => {
      if (watch && watcherId.current !== null) {
        navigator.geolocation.clearWatch(watcherId.current)
      }
    }
  }, [watch])

  return { location, status, error }
}
