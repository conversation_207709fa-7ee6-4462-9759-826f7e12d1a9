import { cn } from '@/lib/utils'
import type { TButtonProps } from './button'
import type { ReactElement, ComponentProps, ReactNode } from 'react'

interface Props extends Omit<ComponentProps<'input'>, 'prefix' | 'suffix'> {
  slim?: boolean
  suffix?: ReactNode
  prefix?: ReactNode
  containerClassName?: string
}

const iconStyles = 'h-full px-2 bg-transparent'

function Input({ className, type, slim, suffix, prefix, containerClassName, ...props }: Props) {
  return (
    <div
      className={cn(
        'h-fit flex items-center border-input rounded-lg border border-input-border-light dark:border-input-border-dark dark:bg-input/30 bg-bg-input-light dark:bg-bg-input-dark overflow-hidden',
        containerClassName
      )}
    >
      {prefix && (
        <div className={cn(iconStyles, (prefix as ReactElement<TButtonProps>)?.props.type == 'button' && 'py-0 px-0!')}>
          {prefix}
        </div>
      )}
      <input
        type={type}
        data-slot="input"
        className={cn(
          'file:text-foreground placeholder:capitalize placeholder:text-text-sub-light dark:placeholder:text-text-sub-dark selection:bg-primary selection:text-primary-foreground flex h-12 w-full min-w-0 px-2 py-1 bg-transparent transition-[color] outline-hidden file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed',
          'aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive  dark:bg-bg-input-dark overflow-hidden text-base md:text-lg bg-bg-input-light disabled:bg-bg-input-disabled-light disabled:dark:bg-input-disabled-dark disabled:text-text-sub-light disabled:dark:text-text-sub-dark disabled:font-medium',
          className,
          slim && 'rounded-s-none'
        )}
        {...props}
      />
      {suffix && (
        <div className={cn(iconStyles, (suffix as ReactElement<TButtonProps>)?.props.type == 'button' && 'py-0 px-0')}>
          {suffix}
        </div>
      )}
    </div>
  )
}

export { Input }
