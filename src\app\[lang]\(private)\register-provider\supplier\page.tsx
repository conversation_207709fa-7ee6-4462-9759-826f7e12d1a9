import RegisterSupplierPage from '@/modules/register-provider/RegisterSupplier'
import { apiService } from '@/services'
import { Metadata } from 'next'

export const dynamic = 'force-dynamic'
export const metadata: Metadata = {
  title: 'Register As Supplier',
  description: 'Fill in the information below to register your company and start using the supplier dashboard.',
}

const Page = async () => {
  const res = await apiService({
    path: '/profile',
  })
  const userData = res?.data?.data

  return <RegisterSupplierPage userData={userData} />
}

export default Page
