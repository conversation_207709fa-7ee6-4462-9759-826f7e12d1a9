import { IFormWrapper } from '@/components/core/FormWrapper'
import useApi from '@/hooks/useApi'
import { useTranslations } from 'next-intl'
import { useEffect, useRef, useState } from 'react'
import { IRegisterSupplierForm } from '../types'
import { useRouter } from 'next/navigation'
import { Routes } from '@/routes/routes'
import { getAddressFromCoordinates } from '@/utils/getAddressFormCoordinates'
import generateBody from '@/utils/generateBody'
// import { getAddressFromCoordinates } from '@/utils/getAddressFormCoordinates'

const useRegisterSupplierForm = ({ userData }: { userData: IRegisterSupplierForm }) => {
  const t = useTranslations()
  const formRef = useRef<IFormWrapper>(null)
  const router = useRouter()
  const [isOpenSuccessDialog, setIsOpenSuccessDialog] = useState(false)
  const { action, isPending } = useApi({
    path: '/user/upgrade-to-supplier',
    method: 'POST',
    handleSuccess: false,
    onSuccess: async () => {
      setIsOpenSuccessDialog(true)
    },
  })

  const handleCloseSuccessDialog = () => {
    setIsOpenSuccessDialog(false)
    router.push(Routes.HOME)
  }

  useEffect(() => {
    const fetchAddress = async () => {
      try {
        if (userData?.location?.lat && userData?.location?.lng) {
          const { address } = await getAddressFromCoordinates(userData.location.lat, userData.location.lng)

          formRef.current?.setValues({
            ...userData,
            location: address || null,
          })
        } else {
          formRef.current?.setValues({
            ...userData,
            location: '',
          })
        }
      } catch (error) {
        console.error('Error fetching address:', error)
      }
    }

    fetchAddress()
  }, [userData])

  const handleSubmit = (payload: IRegisterSupplierForm) => {
    const formdata = generateBody(payload)
    payload.location_lat && formdata.append('location[lat]', payload.location_lat.toString())
    payload.location_lng && formdata.append('location[lng]', payload.location_lng.toString())
    action(formdata)
  }
  return {
    t,
    formRef,
    isPending,
    handleSubmit,
    handleCloseSuccessDialog,
    isOpenSuccessDialog,
  }
}

export default useRegisterSupplierForm
