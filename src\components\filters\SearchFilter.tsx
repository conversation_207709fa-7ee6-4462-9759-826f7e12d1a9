import { Input } from '../ui/input'
import { useQuery } from '@/store/queryContext/useQueryContext'
import { ComponentProps, useCallback, useRef } from 'react'
import { Label } from '../ui/label'
import { X } from 'lucide-react'
import { Button } from '../ui/button'
import { debounce } from '@/lib/utils'
import { useTranslations } from 'next-intl'

export interface ISearchFilterProps extends ComponentProps<'input'> {
  name: string
  label?: string
  placeholder?: string
}

const SearchFilter = ({ name, label, placeholder, ...props }: ISearchFilterProps) => {
  const { forwardAddQuery, forwardDeleteQuery, forwardQuery } = useQuery()
  const t = useTranslations()
  const inputRef = useRef<HTMLInputElement>(null)

  const handleSearch = useCallback(
    debounce((e: React.ChangeEvent<HTMLInputElement>) => {
      if (!e.target.value) {
        handleClear()
        return
      }
      forwardAddQuery({ [name]: e.target.value })
    }),
    []
  )

  const handleClear = useCallback(
    debounce(() => {
      forwardDeleteQuery(name)
      if (inputRef.current) {
        inputRef.current.value = ''
      }
    }, 300),
    []
  )

  return (
    <div>
      {label && <Label htmlFor={name}>{label}</Label>}
      <Input
        defaultValue={forwardQuery?.[name] || ''}
        id={`${name}-id`}
        name={name}
        ref={inputRef}
        placeholder={placeholder || t('label.search')}
        onChange={handleSearch}
        {...props}
        suffix={
          forwardQuery?.[name] && (
            <Button variant={'ghost'} onClick={handleClear}>
              <X />
            </Button>
          )
        }
      />
    </div>
  )
}

export default SearchFilter
