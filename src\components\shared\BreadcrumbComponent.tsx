'use client'

import { Breadcrumb } from '../layouts/Breadcrumb'
import useLastBreadcrumbItem from '@/hooks/useLastBreadcrumbItem'
import { useTranslations } from 'next-intl'

const BreadcrumbComponent = ({ title }: { title?: string }) => {
  const t = useTranslations()
  const lastItem = useLastBreadcrumbItem()

  return (
    <header className="border-b-[2px] border-bg-input-light dark:border-bg-input-dark py-5">
      <div className="relative before:content-[''] before:absolute before:start-0 before:top-[23px] md:before:top-[29px] before:h-[40px] md:before:h-[50px] before:w-[12px] before:bg-secondary before:-translate-y-1/2 px-[26px] before:rounded-[2px]">
        <Breadcrumb />
        <h1 className="text-lg md:text-[32px] font-bold text-text-head-light dark:text-text-head-dark md:leading-[38px] leading-[28px] mt-[7px]">
          {title ||
            (lastItem && typeof lastItem === 'object' && 'label' in lastItem ? t(`navbar.${lastItem.label}`) : '')}
        </h1>
      </div>
    </header>
  )
}

export default BreadcrumbComponent
