// src/components/multi-select.tsx

import * as React from 'react'
import { cva, type VariantProps } from 'class-variance-authority'
import { CheckIcon, XCircle, ChevronDown, XIcon, WandSparkles } from 'lucide-react'

import { cn } from '@/lib/utils'
import { Separator } from '@/components/ui/separator'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover'
import { Command, CommandEmpty, CommandGroup, CommandInput, CommandItem, CommandList } from '@/components/ui/command'
import { useTranslations } from 'next-intl'

/**
 * Variants for the multi-select component to handle different styles.
 * Uses class-variance-authority (cva) to define different styles based on "variant" prop.
 */
export const multiSelectVariants = cva('m-1 transition ease-in-out delay-150 duration-300', {
  variants: {
    variant: {
      default: 'border-foreground/10 text-foreground bg-card hover:bg-card/80',
      secondary: 'border-foreground/10 bg-secondary text-secondary-foreground hover:bg-secondary/80',
      destructive: 'border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80',
      inverted: 'inverted',
    },
  },
  defaultVariants: {
    variant: 'default',
  },
})

/**
 * Props for MultiSelect component
 */
export interface MultiSelectProps<T>
  extends Omit<React.ButtonHTMLAttributes<HTMLButtonElement>, 'onToggle'>,
    VariantProps<typeof multiSelectVariants> {
  /**
   * An array of option objects to be displayed in the multi-select component.
   * Each option object has a label, value, and an optional icon.
   */

  data: T[]

  valueKey: keyof T

  labelKey: keyof T

  iconKey?: keyof T

  /**
   * Callback function triggered when the selected values change.
   * Receives an array of the new selected values.
   */
  onValueChange?: (value: string[]) => void

  /** The default selected values when the component mounts. */
  defaultValue?: string[]

  /**
   * Placeholder text to be displayed when no values are selected.
   * Optional, defaults to "Select options".
   */
  placeholder?: string

  /**
   * Animation duration in seconds for the visual effects (e.g., bouncing badges).
   * Optional, defaults to 0 (no animation).
   */
  animation?: number

  /**
   * Maximum number of items to display. Extra selected items will be summarized.
   * Optional, defaults to 3.
   */
  maxCount?: number

  /**
   * The modality of the popover. When set to true, interaction with outside elements
   * will be disabled and only popover content will be visible to screen readers.
   * Optional, defaults to false.
   */
  modalPopover?: boolean

  /**
   * If true, renders the multi-select component as a child of another component.
   * Optional, defaults to false.
   */
  asChild?: boolean

  /**
   * Additional class names to apply custom styles to the multi-select component.
   * Optional, can be used to add custom styles.
   */
  className?: string

  onToggle?: (isOpen: boolean) => void

  onSearch?: (value: string) => void
  onClear?: () => void
}

export const MultiSelect = React.forwardRef<HTMLButtonElement, MultiSelectProps<any>>(
  (
    {
      data,
      onValueChange,
      variant,
      defaultValue = [],
      placeholder,
      animation = 0,
      maxCount = 3,
      modalPopover = false,
      asChild = false,
      className,
      valueKey,
      labelKey,
      iconKey,
      onToggle,
      onSearch,
      onClear,
      ...props
    },
    ref
  ) => {
    const [selectedValues, setSelectedValues] = React.useState<string[]>(defaultValue)
    const [isPopoverOpen, setIsPopoverOpen] = React.useState(false)
    const [isAnimating, setIsAnimating] = React.useState(false)
    const t = useTranslations()

    const buttonRef = React.useRef<HTMLButtonElement | null>(null)
    const [popoverWidth, setPopoverWidth] = React.useState<number | undefined>(undefined)

    React.useEffect(() => {
      if (isPopoverOpen && buttonRef.current) {
        setPopoverWidth(buttonRef.current.offsetWidth)
      }
    }, [isPopoverOpen, selectedValues])

    const handleInputKeyDown = (event: React.KeyboardEvent<HTMLInputElement>) => {
      if (event.key === 'Enter') {
        setIsPopoverOpen(true)
      } else if (event.key === 'Backspace' && !event.currentTarget.value) {
        const newSelectedValues = [...selectedValues]
        newSelectedValues.pop()
        setSelectedValues(newSelectedValues)
        onValueChange && onValueChange(newSelectedValues)
      }
    }

    const toggleOption = (option: string) => {
      const newSelectedValues = selectedValues.includes(option)
        ? selectedValues.filter((value) => value !== option)
        : [...selectedValues, option]
      setSelectedValues(newSelectedValues)
      onValueChange && onValueChange(newSelectedValues)
      newSelectedValues.length === 0 && onClear && onClear()
    }

    const handleClear = () => {
      setSelectedValues([])
      onValueChange && onValueChange([])
      onClear && onClear()
    }

    const handleTogglePopover = () => {
      setIsPopoverOpen((prev) => !prev)
    }

    const clearExtraOptions = () => {
      const newSelectedValues = selectedValues.slice(0, maxCount)
      setSelectedValues(newSelectedValues)
      onValueChange && onValueChange(newSelectedValues)
    }

    const toggleAll = () => {
      if (selectedValues.length === data?.length) {
        handleClear()
      } else {
        const allValues = data.map((option) => option[valueKey])
        setSelectedValues(allValues)
        onValueChange && onValueChange(allValues)
      }
    }

    return (
      <Popover
        open={isPopoverOpen}
        onOpenChange={(open) => {
          setIsPopoverOpen(open)
          onToggle?.(open)
        }}
        modal={modalPopover}
      >
        <PopoverTrigger asChild>
          <Button
            ref={(node) => {
              if (typeof ref === 'function') ref(node)
              else if (ref) (ref as React.MutableRefObject<HTMLButtonElement | null>).current = node
              buttonRef.current = node
            }}
            {...props}
            variant={'text'}
            onClick={handleTogglePopover}
            className={cn(
              'flex w-full p-1 rounded-lg border border-input-border-light dark:border-input-border-dark h-fit min-h-[50px] items-center justify-between [&_svg]:pointer-events-auto bg-bg-input-light dark:bg-bg-input-dark text-lg',
              className
            )}
          >
            {selectedValues.length > 0 ? (
              <div className="flex justify-between items-center w-full">
                <div className="flex flex-wrap items-center">
                  {selectedValues.slice(0, maxCount).map((value) => {
                    const option = data.find((o) => o[valueKey] === value)
                    const IconComponent = option?.[iconKey || 'icon']
                    return (
                      <Badge
                        key={value}
                        className={cn(
                          '[&>svg]:pointer-events-auto',
                          isAnimating ? 'animate-bounce' : '',
                          multiSelectVariants({ variant })
                        )}
                        style={{ animationDuration: `${animation}s` }}
                      >
                        {IconComponent && <IconComponent className="h-4 w-4 mr-2" />}
                        {option?.[labelKey]}
                        <XCircle
                          className="ml-2 h-4 w-4 cursor-pointer"
                          onClick={(event) => {
                            event.stopPropagation()
                            toggleOption(value)
                          }}
                        />
                      </Badge>
                    )
                  })}
                  {selectedValues.length > maxCount && (
                    <Badge
                      className={cn(
                        'bg-transparent text-foreground border-foreground/1 [&>svg]:pointer-events-auto border-input-border-light dark:border-input-border-dark border',
                        isAnimating ? 'animate-bounce' : '',
                        multiSelectVariants({ variant })
                      )}
                      style={{ animationDuration: `${animation}s` }}
                    >
                      {`+ ${selectedValues.length - maxCount} ${t('label.more')}`}
                      <XCircle
                        className="ml-2 h-4 w-4 cursor-pointer pointer-events-auto"
                        onClick={(event) => {
                          event.stopPropagation()
                          clearExtraOptions()
                        }}
                      />
                    </Badge>
                  )}
                </div>
                <div className="flex items-center justify-between">
                  <XIcon
                    className="h-4 mx-2 cursor-pointer text-muted-foreground"
                    onClick={(event) => {
                      event.stopPropagation()
                      handleClear()
                    }}
                  />
                  <Separator orientation="vertical" className="flex min-h-6 h-full" />
                  <ChevronDown className="h-4 mx-2 cursor-pointer text-muted-foreground" />
                </div>
              </div>
            ) : (
              <div className="flex items-center justify-between w-full mx-auto">
                <span className="text-sm md:text-lg text-muted-foreground font-normal mx-3">{placeholder}</span>
                <ChevronDown className="h-4 cursor-pointer text-muted-foreground mx-2" />
              </div>
            )}
          </Button>
        </PopoverTrigger>
        <PopoverContent
          className="w-full p-0"
          align="start"
          style={{
            width: popoverWidth ? `${popoverWidth}px` : undefined,
            minWidth: popoverWidth ? `${popoverWidth}px` : undefined,
          }}
          onEscapeKeyDown={() => {
            setIsPopoverOpen(false)
          }}
        >
          <Command shouldFilter={!onSearch} className="bg-bg-input-light dark:bg-bg-input-dark">
            <CommandInput
              placeholder={t('label.search')}
              onKeyDown={handleInputKeyDown}
              onValueChange={(value) => {
                onSearch && onSearch?.(value)
              }}
            />
            <CommandList>
              <CommandEmpty>{t('label.no_results')}</CommandEmpty>
              <CommandGroup>
                <CommandItem key="all" onSelect={toggleAll} className="cursor-pointer">
                  <div
                    className={cn(
                      'mr-2 flex h-4 w-4 items-center justify-center rounded-xs border dark:border-input-border-light border-input-border-dark',
                      selectedValues.length === data?.length
                        ? 'bg-primary text-primary-foreground'
                        : 'opacity-50 [&_svg]:invisible'
                    )}
                  >
                    <CheckIcon className="h-4 w-4" />
                  </div>
                  <span>{t('label.select_all')}</span>
                </CommandItem>
                {(data ?? []).map((option) => {
                  const isSelected = selectedValues.includes(option[valueKey])
                  return (
                    <CommandItem
                      key={option[valueKey]}
                      onSelect={() => toggleOption(option[valueKey])}
                      className="cursor-pointer"
                    >
                      <div
                        className={cn(
                          'mr-2 flex h-4 w-4 items-center justify-center rounded-xs border dark:border-input-border-light border-input-border-dark',
                          isSelected ? 'bg-primary text-white dark:bg-secondary' : 'opacity-50 [&_svg]:invisible'
                        )}
                      >
                        <CheckIcon className="h-4 w-4" />
                      </div>
                      {iconKey && option[iconKey] && <option.icon className="mr-2 h-4 w-4 text-muted-foreground" />}
                      <span>{option[labelKey]}</span>
                    </CommandItem>
                  )
                })}
              </CommandGroup>
            </CommandList>
          </Command>
        </PopoverContent>
        {animation > 0 && selectedValues.length > 0 && (
          <WandSparkles
            className={cn(
              'cursor-pointer my-2 text-foreground bg-background w-3 h-3',
              isAnimating ? '' : 'text-muted-foreground'
            )}
            onClick={() => setIsAnimating(!isAnimating)}
          />
        )}
      </Popover>
    )
  }
)

MultiSelect.displayName = 'MultiSelect'
